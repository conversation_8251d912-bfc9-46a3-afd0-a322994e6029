package app

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
)

// CreatePublicLink implements model.ClientUsecase.
func (c *clientUsecase) CreatePublicLink(ctx context.Context, link model.PublicClientLinkCreate) error {
	newLink := model.PublicClientLink{
		ID:         link.ID,
		ScheduleID: link.ScheduleID,
		TurnID:     link.TurnID,
		WorkerIDs:  link.WorkerIDs,
		ClientID:   link.ClientID,
		URL:        link.URL,
	}

	return c.repo.CreatePublicLink(ctx, newLink)
}
