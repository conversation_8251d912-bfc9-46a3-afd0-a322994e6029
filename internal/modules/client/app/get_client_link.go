package app

import (
	"context"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
)

// GetClientLink implements model.ClientUsecase.
// This method returns all sessions registered on schedule id, turn id and workers that have sessions with the client.
// It's similar to get all sessions method but filtered by schedule, turn and workers.
func (c *clientUsecase) GetClientLink(ctx context.Context, scheduleID, turnID string, workerIDs []string) (*model.ClientLinkResponse, error) {
	return c.repo.GetClientLink(ctx, scheduleID, turnID, workerIDs)
}
