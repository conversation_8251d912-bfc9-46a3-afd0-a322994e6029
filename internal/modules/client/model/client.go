package model

import (
	"time"

	personModel "github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
)

type Client struct {
	ID         string
	Person     *personModel.Person
	PublicLink *string // URL from public_client_links table (only one per client)
	CreatedAt  *time.Time
	UpdatedAt  *time.Time
	DeletedAt  *time.Time
}

type ClientCreate struct {
	PersonCreate personModel.PersonCreate
}

type ClientUpdate struct {
	ID           string
	PersonUpdate personModel.PersonUpdate
}

type PublicClientLink struct {
	ID         string
	ScheduleID string
	TurnID     string
	WorkerIDs  []string
	ClientID   string
	URL        string
	CreatedAt  *time.Time
	UpdatedAt  *time.Time
	DeletedAt  *time.Time
}

type PublicClientLinkCreate struct {
	ID         string
	ScheduleID string
	TurnID     string
	WorkerIDs  []string
	ClientID   string
	URL        string
}

type PublicClientLinkUpdate struct {
	ID         string
	ScheduleID string
	TurnID     string
	WorkerIDs  []string
	ClientID   string
	URL        string
}

// ClientLinkResponse represents the response for GetClientLink method
// Contains sessions filtered by schedule, turn and workers
type ClientLinkResponse struct {
	Sessions []SessionInfo `json:"sessions"`
}

// SessionInfo represents session information without circular dependencies
type SessionInfo struct {
	ID        string     `json:"id"`
	ClientID  *string    `json:"client_id"`
	WorkerID  string     `json:"worker_id"`
	TurnID    string     `json:"turn_id"`
	Day       int        `json:"day"`
	Time      int        `json:"time"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}
