package model

import "context"

type ClientRepository interface {
	Create(ctx context.Context, client Client) error
	Update(ctx context.Context, client Client) error
	GetByProp(ctx context.Context, prop string, value string) (*Client, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]Client, error)
	Delete(ctx context.Context, id string) error

	// Public Client Link methods
	CreatePublicLink(ctx context.Context, link PublicClientLink) error
	UpdatePublicLink(ctx context.Context, link PublicClientLink) error
	GetClientLink(ctx context.Context, scheduleID, turnID string, workerIDs []string) (*ClientLinkResponse, error)
	DeletePublicLink(ctx context.Context, id string) error
}
