package model

import "context"

type ClientUsecase interface {
	Create(ctx context.Context, client ClientCreate) (string, error)
	Update(ctx context.Context, client ClientUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*Client, error)
	GetAll(ctx context.Context) ([]Client, error)
	Delete(ctx context.Context, id string) error

	// Public Client Link methods
	GeneratePublicLink() string
	CreatePublicLink(ctx context.Context, link PublicClientLinkCreate) error
	UpdatePublicLink(ctx context.Context, link PublicClientLinkUpdate) error
	GetClientLink(ctx context.Context, scheduleID, turnID string, workerIDs []string) (*ClientLinkResponse, error)
	DeletePublicLink(ctx context.Context, id string) error
}
