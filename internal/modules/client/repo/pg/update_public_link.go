package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *clientPostgreRepo) UpdatePublicLink(ctx context.Context, link model.PublicClientLink) error {
	return pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            UPDATE public_client_links
            SET schedule_id = $2, turn_id = $3, worker_ids = $4, client_id = $5, url = $6, updated_at = CURRENT_TIMESTAMP
            WHERE id = $1 AND deleted_at IS NULL
        `

		result, err := conn.Exec(ctx, query,
			link.ID,
			link.ScheduleID,
			link.TurnID,
			link.WorkerIDs,
			link.ClientID,
			link.URL,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update public client link", err, nil)
		}

		if result.RowsAffected() == 0 {
			return utils.NotFoundf(
				fmt.Sprintf("public client link with id: %s not found", link.ID),
				fmt.Errorf("public client link not found or already deleted"),
				nil,
			)
		}

		return nil
	})
}
